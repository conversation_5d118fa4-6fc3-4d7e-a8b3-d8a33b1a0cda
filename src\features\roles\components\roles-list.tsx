import { useEffect } from 'react'
import { useRoles } from '../context/roles-context'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { IconShield, IconPlus } from '@tabler/icons-react'
import { Role } from '../data/schema'

export default function RolesList() {
  const { roles, fetchRoles, setCurrentRole, fetchCommandItems, setActiveTab, setOpen } = useRoles()

  useEffect(() => {
    fetchRoles()
  }, [fetchRoles])

  const handleRoleClick = (role: Role) => {
    setCurrentRole(role)
    fetchCommandItems(role.roleName)
    setActiveTab('commands') // Switch to commands tab
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-medium">Roles</h3>
        <Button
          size="sm"
          onClick={() => setOpen('addRole')}
        >
          <IconPlus className="mr-2 h-4 w-4" />
          Add Role
        </Button>
      </div>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Role Name</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {roles.map((role) => (
            <TableRow key={role.roleName}>
              <TableCell className="font-medium">
                <div className="flex items-center gap-2">
                  <IconShield size={16} className="text-muted-foreground" />
                  {role.roleName}
                </div>
              </TableCell>
              <TableCell>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => handleRoleClick(role)}
                >
                  View Commands
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}