import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { useRoles } from '../context/roles-context'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { IconShieldPlus } from '@tabler/icons-react'

const formSchema = z.object({
  roleName: z.string().min(1, { message: 'Role name is required.' }),
})

type RoleForm = z.infer<typeof formSchema>

export default function AddRoleDialog() {
  const { open, setOpen, addRole } = useRoles()
  
  const form = useForm<RoleForm>({
    resolver: zodResolver(formSchema),
    defaultValues: { roleName: '' },
  })

  const onSubmit = async (values: RoleForm) => {
    try {
      await addRole({
        id: 0, // Temporary ID, will be assigned by backend
        roleName: values.roleName
      })
      form.reset()
      setOpen(null)
    } catch (error) {
      console.error('Error adding role:', error)
    }
  }

  return (
    <Dialog
      open={open === 'addRole'}
      onOpenChange={(isOpen) => {
        if (!isOpen) {
          form.reset()
          setOpen(null)
        }
      }}
    >
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <IconShieldPlus size={20} />
            Add New Role
          </DialogTitle>
          <DialogDescription>
            Create a new role in the system.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form
            id="add-role-form"
            onSubmit={form.handleSubmit(onSubmit)}
            className="space-y-4"
          >
            <FormField
              control={form.control}
              name="roleName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Role Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter role name (e.g., ROLE_ADMIN)" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </form>
        </Form>
        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => {
              form.reset()
              setOpen(null)
            }}
          >
            Cancel
          </Button>
          <Button type="submit" form="add-role-form">
            Add Role
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
