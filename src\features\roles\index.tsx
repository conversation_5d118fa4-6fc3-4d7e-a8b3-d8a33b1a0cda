import { <PERSON>, CardContent, CardDes<PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs'
import { IconShield } from '@tabler/icons-react'
import Roles<PERSON>rovider, { useRoles } from './context/roles-context'
import RolesList from './components/roles-list'
import CommandItemsList from './components/command-items-list'
import AddCommandItemDialog from './components/add-command-item-dialog'
import AddRoleDialog from './components/add-role-dialog'
import { Main } from '@/components/layout/main'
import { Search } from '@/components/search'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { ThemeSwitch } from '@/components/theme-switch'
import { Header } from '@/components/layout/header'

function RolesContent() {
  const { activeTab, setActiveTab } = useRoles()

  return (
    <><Header>
      <Search />
      <div className='ml-auto flex items-center space-x-4'>
        <ThemeSwitch />
        <ProfileDropdown />
      </div>
    </Header><Main>
        <div className="flex flex-col gap-5 p-4 md:p-8">
          <div className="mb-2 flex flex-wrap items-center justify-between space-y-2">
            <div>
              <h2 className="text-2xl font-bold tracking-tight flex items-center gap-2">
                <IconShield className="text-primary" size={24} />
                Role Management
              </h2>
              <p className="text-muted-foreground">
                Manage roles and their command permissions
              </p>
            </div>
          </div>
          <Card>
            <CardHeader>
              <CardTitle>Roles and Commands</CardTitle>
              <CardDescription>
                View all roles and manage their command permissions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                <TabsList className="mb-4">
                  <TabsTrigger value="roles">Roles</TabsTrigger>
                  <TabsTrigger value="commands">Command Permissions</TabsTrigger>
                </TabsList>
                <TabsContent value="roles">
                  <RolesList />
                </TabsContent>
                <TabsContent value="commands">
                  <CommandItemsList />
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
          <AddCommandItemDialog />
          <AddRoleDialog />
        </div>
      </Main></>
  )
}

export default function Roles() {
  return (
    <RolesProvider>
      <RolesContent />
    </RolesProvider>
  )
}