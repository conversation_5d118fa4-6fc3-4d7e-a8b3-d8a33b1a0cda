import React, { useState, useRef, useEffect } from 'react';
import Editor from "@monaco-editor/react";
import YAML from 'yaml';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogDescription
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { IconCopy, IconDeviceFloppy } from '@tabler/icons-react';
import axios_api from '@/lib/axios_api';

interface NetworkYamlEditorProps {
  open: boolean;
  yamlContent: string;
  onClose: () => void;
}

/**
 * NetworkYamlEditor component for viewing and editing Kubernetes network policy YAML
 */
const NetworkYamlEditor: React.FC<NetworkYamlEditorProps> = ({ 
  open, 
  yamlContent, 
  onClose 
}) => {
  // State management
  const [yaml, setYaml] = useState<string>(yamlContent);
  const [isEditable, setIsEditable] = useState<boolean>(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState<boolean>(false);
  const [isValidYaml, setIsValidYaml] = useState<boolean>(true);
  
  // Editor reference
  const editorRef = useRef<any>(null);

  // Update YAML content when prop changes
  useEffect(() => {
    setYaml(yamlContent);
  }, [yamlContent]);

  // Handle editor initialization
  const handleEditorDidMount = (editor: any) => {
    editorRef.current = editor;
  };

  // Validate and update YAML content
  const handleYamlChange = (value: string | undefined) => {
    if (!value) return;
    
    try {
      // Validate YAML syntax
      YAML.parse(value);
      setYaml(value);
      setIsValidYaml(true);
    } catch (error) {
      console.error("Invalid YAML:", error);
      setYaml(value);
      setIsValidYaml(false);
    }
  };

  // Copy YAML to clipboard
  const copyToClipboard = () => {
    navigator.clipboard.writeText(yaml)
      .then(() => toast.success("YAML copied to clipboard"))
      .catch(() => toast.error("Failed to copy YAML"));
  };

  // Apply YAML to create/update network policy
  const handleApply = async () => {
    try {
       await axios_api.post("/k8s/network-policy/apply", yaml, {
        headers: {
          "Content-Type": "text/yaml",
        },
      });

      toast.success("Network policy successfully applied");
      setShowConfirmDialog(false);
      setIsEditable(false);
      onClose();
    } catch (error: any) {
      console.error("Error applying network policy:", error);
      toast.error(`Failed to apply network policy: ${error.response?.data?.message || error.message}`);
    }
  };

  return (
    <>
      {/* Main YAML Editor Dialog */}
      <Dialog open={open} onOpenChange={(isOpen) => !isOpen && onClose()}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Network Policy YAML</DialogTitle>
            <DialogDescription>
              View or edit the Kubernetes Network Policy YAML configuration
            </DialogDescription>
          </DialogHeader>
          
          <div className="h-[500px] w-full">
            <Editor
              width="100%"
              height="100%"
              language="yaml"
              value={yaml}
              theme="vs-dark"
              options={{
                selectOnLineNumbers: true,
                roundedSelection: false,
                readOnly: !isEditable,
                cursorStyle: 'line',
                automaticLayout: true,
                wordWrap: 'on',
                scrollBeyondLastLine: false,
                minimap: { enabled: true },
                lineNumbers: 'on',
                folding: true,
                renderLineHighlight: 'all',
              }}
              onChange={handleYamlChange}
              onMount={handleEditorDidMount}
            />
          </div>
          
          {!isValidYaml && isEditable && (
            <div className="text-destructive text-sm">
              Warning: The YAML contains syntax errors
            </div>
          )}
          
          <DialogFooter className="flex justify-between">
            <div className="flex items-center space-x-2">
              <Switch
                id="edit-mode"
                checked={isEditable}
                onCheckedChange={setIsEditable}
              />
              <Label htmlFor="edit-mode">{isEditable ? "Edit Mode" : "View Mode"}</Label>
            </div>

            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={copyToClipboard}
              >
                <IconCopy size={16} className="mr-1" />
                Copy
              </Button>
              
              {isEditable && (
                <Button
                  variant="default"
                  size="sm"
                  onClick={() => setShowConfirmDialog(true)}
                  disabled={!isValidYaml}
                >
                  <IconDeviceFloppy size={16} className="mr-1" />
                  Apply
                </Button>
              )}
              
              <Button variant="secondary" size="sm" onClick={onClose}>
                Close
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Confirmation Dialog */}
      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Apply Network Policy</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to apply this network policy? This action will affect network traffic rules in your Kubernetes cluster.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleApply}>Apply</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default NetworkYamlEditor;