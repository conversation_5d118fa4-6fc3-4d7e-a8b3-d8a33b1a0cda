import { useRoles } from '../context/roles-context'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { IconPlus, IconTrash } from '@tabler/icons-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

export default function CommandItemsList() {
  const { commandItems, currentRole, setOpen, deleteCommandItem } = useRoles()

  const handleDeleteCommand = async (item: any) => {
    if (window.confirm(`Are you sure you want to delete the command "${item.commandName}"?`)) {
      try {
        await deleteCommandItem(item)
      } catch (error) {
        console.error('Error deleting command:', error)
      }
    }
  }

  if (!currentRole) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-muted-foreground">
            Select a role from the Roles tab to view its command permissions
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-medium">
          Command Permissions for: <span className="font-bold">{currentRole.roleName}</span>
        </h3>
        <Button 
          size="sm" 
          onClick={() => setOpen('add')}
        >
          <IconPlus className="mr-2 h-4 w-4" />
          Add Command
        </Button>
      </div>

      {commandItems.length === 0 ? (
        <div className="text-center p-4 border rounded-md bg-muted/20">
          No command permissions found for this role
        </div>
      ) : (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Command Name</TableHead>
              <TableHead>Description</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {commandItems.map((item) => (
              <TableRow key={`${item.roleName}-${item.commandName}`}>
                <TableCell className="font-medium">{item.commandName}</TableCell>
                <TableCell>{item.description || 'No description'}</TableCell>
                <TableCell>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDeleteCommand(item)}
                    className="text-destructive hover:text-destructive"
                  >
                    <IconTrash className="h-4 w-4" />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      )}
    </div>
  )
}