import React, { useState, useEffect, useCallback } from 'react'
import useDialogState from '@/hooks/use-dialog-state'
import { User, userListSchema } from '../data/schema'
import axios_api from '@/lib/axios_api'
import { toast } from 'sonner'


type UsersDialogType = 'invite' | 'add' | 'edit' | 'delete' | 'roles'

interface UsersContextType {
  open: UsersDialogType | null
  setOpen: (str: UsersDialogType | null) => void
  currentRow: User | null
  setCurrentRow: React.Dispatch<React.SetStateAction<User | null>>
  users: User[]
  roles: string[]
  fetchUsers: () => Promise<void>
  assignRoles: (username: string, roles: string[]) => Promise<void>
  deleteUser: (username: string) => Promise<void>
}

const UsersContext = React.createContext<UsersContextType | null>(null)

interface Props {
  children: React.ReactNode
}

export default function UsersProvider({ children }: Props) {
  const [open, setOpen] = useDialogState<UsersDialogType>(null)
  const [currentRow, setCurrentRow] = useState<User | null>(null)
  const [users, setUsers] = useState<User[]>([])
  const [roles, setRoles] = useState<string[]>([])

  const fetchUsers = useCallback(async () => {
    try {
      const response = await axios_api.get('/auth/users')
      // Parse the response data through the schema
      const userList = userListSchema.parse(response.data)
      setUsers(userList)
    } catch (err) {
    
    }
  }, [])

  const fetchRoles = useCallback(async () => {
    try {
      const response = await axios_api.get('/auth/roles')
      // Extract role names from the response
      const roleNames = response.data.map((role: string) => 
        role.startsWith('ROLE_') ? role : `ROLE_${role}`
      )
      setRoles(roleNames)
    } catch (err) {
    
    }
  }, [])

  const assignRoles = async (username: string, selectedRoles: string[]) => {
    try {
      // Ensure roles have the ROLE_ prefix if needed
      const formattedRoles = selectedRoles.map(role => 
        role.startsWith('ROLE_') ? role : `ROLE_${role}`
      )
      
      // Get the current user to see their existing roles
      const currentUser = users.find(user => user.username === username);
      
      if (!currentUser) {
        throw new Error(`User ${username} not found`);
      }
      
      // Make the API call with the selected roles
      await axios_api.post(`/auth/assign-roles?username=${username}`, formattedRoles)
      
      // Refresh the users list to get updated roles
      await fetchUsers()
      toast.success(`Roles updated successfully for ${username}`)
      return Promise.resolve()
    } catch (error) {
      return Promise.reject(error)
    }
  }

  const deleteUser = async (username: string) => {
    try {
      await axios_api.delete(`/auth/by-username/${username}`)
      toast.success(`User ${username} deleted successfully`)
      await fetchUsers()
      return Promise.resolve()
    } catch (error) {
      return Promise.reject(error)
    }
  }

  useEffect(() => {
    fetchUsers()
    fetchRoles()
  }, [fetchUsers, fetchRoles])

  return (
    <UsersContext.Provider value={{ 
      open, 
      setOpen, 
      currentRow, 
      setCurrentRow, 
      users, 
      roles, 
      fetchUsers,
      assignRoles,
      deleteUser
    }}>
      {children}
    </UsersContext.Provider>
  )
}

// eslint-disable-next-line react-refresh/only-export-components
export const useUsers = () => {
  const usersContext = React.useContext(UsersContext)

  if (!usersContext) {
    throw new Error('useUsers has to be used within <UsersContext.Provider>')
  }

  return usersContext
}
